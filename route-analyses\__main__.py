"""
__main__.py – CLI entry for the lightweight Route‑Savings test harness.

Example:
    python -m route_analyses --config config.local.yaml --output route_savings.csv
"""

from pathlib import Path
import argparse
from db import Database
from analyses import run_route_analyses


def main() -> None:
    parser = argparse.ArgumentParser(description="Run Route Savings analysis")
    parser.add_argument(
        "--config",
        default="config.local.yaml",
        help="Path to YAML config (default: %(default)s)",
    )
    parser.add_argument(
        "--output", default="route_savings.csv", help="CSV file to write results to"
    )
    parser.add_argument(
        "--days", type=int, default=30, help="Look‑back window in days (default: %(default)s)"
    )
    args = parser.parse_args()

    db = Database(args.config)
    result = run_route_analyses(db, days=args.days)
    result.write_csv(args.output)
    print(f"✓ Wrote {len(result)} rows to {Path(args.output).resolve()}")


if __name__ == "__main__":
    main()