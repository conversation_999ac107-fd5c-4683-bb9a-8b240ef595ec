import datetime, sys, time, traceback, logging

from typing import Optional, Dict

from selenium.webdriver import Firefox
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

print("Starting OKX Turkey Clicker...")

from common import (
    TransactionInfoDao,
    Transaction,
    COMPANY_NAME,
    COMPANY_NUMBER,
    TAX_NUMBER,
    main_app,
    EXCHANGE_MAPPING,
)

DEPOSIT_REASON = "Internal transfer"

HOST = "https://tr.okx.com"
DEPOSIT_HISTORY = f"{HOST}/balance/deposit-history"
WITHDRAW_HISTORY = f"{HOST}/balance/withdrawal-history"

SOURCE = 503  # Assuming a different source ID for OKX


class OkxTrClicker:

    def __init__(self, driver: Firefox, trx_dao: TransactionInfoDao, config: Dict):
        self.driver: Firefox = driver
        self.trx_dao: TransactionInfoDao = trx_dao
        self.okx_tab: Optional[str] = None

    def sleep(self, sleep_for, comment: Optional[str] = None):
        if comment:
            print(f"Sleeping for {sleep_for}s: {comment}")
        time.sleep(sleep_for)

    def switch_to_okx_tab(self):
        print(f"Looking for OKX tab in {len(self.driver.window_handles)} open tabs...")
        for wh in self.driver.window_handles:
            self.driver.switch_to.window(wh)
            url = self.driver.current_url
            print(f"Tab {wh}: {url}")
            if "tr.okx.com" in url:
                self.okx_tab = wh
                print(f"Found OKX tab: {wh}")
                return
        raise Exception("Failed to find OKX tab")

    def check_language(self):
        """Check if page is in Turkish/English, adjust if needed"""
        try:
            # Look for language selector or Turkish text indicators
            current_lang = self.driver.find_element(By.XPATH, "//html").get_attribute("lang")

            print(f"Current language: {current_lang}")
            
            # For now, we'll just log the current language
            # Language switching logic can be added here later if needed
            
        except Exception as e:
            print(f"Error checking language: {e}")

    def check_login_status(self):
        """Check if user is logged into OKX"""
        print(f"Checking login status...")
        print(f"Current URL: {self.driver.current_url}")
        
        if self.driver.current_url != HOST:
            print(f"Navigating to {HOST}")
            self.driver.get(HOST)
        self.driver.refresh()
        self.sleep(3, "waiting for login refresh to finish")

        print(f"Page title: {self.driver.title}")
        print(f"Final URL: {self.driver.current_url}")
        
        self.check_language()
        
        try:
            # Check for login indicators - these will need to be updated based on actual OKX page structure
            # Common indicators: presence of user menu, absence of "Sign In" button, etc.
            
            # Look for "Sign In" or "Login" buttons which indicate not logged in
            login_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Sign In') or contains(text(), 'Login') or contains(text(), 'Log In')]")
            print(f"Found {len(login_buttons)} login buttons")
            if login_buttons:
                print("User is not logged in (found login button)")
                raise Exception("OKX not logged in")
            
            # Look for user account indicators
            user_menu = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'user') or contains(@class, 'profile') or contains(@class, 'account')]")
            print(f"Found {len(user_menu)} user menu elements")
            if user_menu:
                print("User appears to be logged in (found user menu)")
                return
            
            # If neither found, assume not logged in for safety
            print("Could not determine login status - assuming not logged in")
            raise Exception("OKX login status unclear")
            
        except Exception as e:
            if "not logged in" in str(e):
                raise
            raise Exception("OKX login check failed")

    def process_deposits(self):
        """Process deposit transactions - placeholder for future implementation"""
        print("Processing deposits...")
        self.driver.get(DEPOSIT_HISTORY)
        self.sleep(5, "deposit history load")
        
        # TODO: Implement deposit processing logic similar to Bybit
        # This would include:
        # - Finding transactions that need details
        # - Finding transactions that need declarations
        # - Filling out forms automatically
        print("Deposit processing completed (placeholder)")

    def process_withdrawals(self):
        """Process withdrawal transactions - placeholder for future implementation"""
        print("Processing withdrawals...")
        self.driver.get(WITHDRAW_HISTORY)
        self.sleep(5, "withdrawal history load")
        
        # TODO: Implement withdrawal processing logic similar to Bybit
        print("Withdrawal processing completed (placeholder)")

    def run(self):
        """Main loop for the OKX clicker"""
        print("Starting OKX clicker main loop...")
        
        while True:
            try:
                print("\n--- Starting new loop iteration ---")
                self.switch_to_okx_tab()
                self.check_login_status()
                
                # For now, we'll just verify login status
                # Uncomment these when ready to implement:
                # self.process_deposits()
                # self.process_withdrawals()
                
                print("Refreshing page...")
                self.driver.refresh()
                
                print("Loop iteration completed successfully!")
                
            except Exception as e:
                print(f"Error in main loop: {e}")
                print(f"Full traceback: {traceback.format_exc()}")
                print("Continuing to next iteration...")

            self.sleep(60, "end of loop")


def main():
    main_app(OkxTrClicker)


if __name__ == "__main__":
    main()