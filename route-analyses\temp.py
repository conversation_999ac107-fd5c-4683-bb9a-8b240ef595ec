import sys
from pathlib import Path
import pandas as pd  # pip install pandas pyarrow

def main():
    if len(sys.argv) < 2:
        sys.exit("Usage: parquet2csv.py <input.parquet> [output.csv]")

    in_path  = Path(sys.argv[1])
    out_path = Path(sys.argv[2]) if len(sys.argv) >= 3 else in_path.with_suffix(".csv")

    print(f"Reading  {in_path} …")
    df = pd.read_parquet(in_path, engine="pyarrow")  # fast columnar read

    print(f"Writing  {out_path} …")
    df.to_csv(out_path, index=False)                 # no row index in CSV

    print("Done ✅")

if __name__ == "__main__":
    main()