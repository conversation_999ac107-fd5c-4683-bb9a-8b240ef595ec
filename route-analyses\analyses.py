"""
Analysis pipelines for route optimization.
"""

from __future__ import annotations

import polars as pl


def build_base_pipeline(t: dict[str, pl.LazyFrame]) -> pl.LazyFrame:
    """Build the base pipeline up to the point where we can apply different filters."""

    transfers = t["transfers"]
    asset_info = t["asset_info"]
    sources_map = t["sources_map"]
    withdrawal_fees = t["withdrawal_fees"]

    # 1. Aggregate transfers locally (unchanged syntax)
    recent_transfers = (
        transfers
        .group_by(["gt_asset", "network", "gt_source_from", "gt_source_to"])
        .agg(
            pl.len().alias("transfer_count"),
            pl.col("gt_fee_in_usdt").sum().alias("fee_paid_usdt"),
        )
    )

    # 2. Exchange mappings (keep your original approach)
    exchange_map = sources_map.select("gt_source", "exchange")

    sm_from = exchange_map.select(
        "gt_source",
        pl.col("exchange").alias("exchange_from"),
    )
    sm_to = exchange_map.select(
        "gt_source",
        pl.col("exchange").alias("exchange_to"),
    )

    # 3. Route candidates (maintain your join pattern)
    route_candidates = (
        recent_transfers
        .join(sm_from, left_on="gt_source_from", right_on="gt_source")
        .join(sm_to, left_on="gt_source_to", right_on="gt_source")
        .select(
            "gt_asset",
            pl.col("network").alias("current_network"),
            "gt_source_from", "gt_source_to",
            "exchange_from", "exchange_to",
            "transfer_count", "fee_paid_usdt",
        )
    )

    # 4. Network counts (keep your prefix approach)
    a1 = asset_info
    a2 = asset_info.rename({c: f"b_{c}" for c in asset_info.collect_schema().names()})

    network_counts = (
        a1.join(
            a2,
            left_on=["gt_asset", "network"],
            right_on=["b_gt_asset", "b_network"],
            how="inner",
        )
        .filter(
            (pl.col("gt_source") != pl.col("b_gt_source")) &
            pl.col("can_withdraw") &
            pl.col("b_can_deposit")
        )
        .group_by(
            "gt_asset",
            pl.col("gt_source").alias("gt_source_from"),
            pl.col("b_gt_source").alias("gt_source_to"),
        )
        .agg(pl.col("network").n_unique().alias("network_count"))
    )

    # 5. Cheapest alternatives (maintain your exact pattern)
    wf = withdrawal_fees.select("gt_asset", "network", "gt_source", "gt_fee_in_usdt")

    cheapest_alt = (
        a1.join(
            a2,
            left_on=["gt_asset", "network"],
            right_on=["b_gt_asset", "b_network"],
            how="inner",
        )
        .filter(pl.col("can_withdraw") & pl.col("b_can_deposit"))
        .join(
            wf,
            left_on=["gt_asset", "network", "gt_source"],
            right_on=["gt_asset", "network", "gt_source"],
            how="left",
        )
        .group_by(
            "gt_asset",
            pl.col("network").alias("suggested_cheaper_network"),
            pl.col("gt_source").alias("source_from"),
            pl.col("b_gt_source").alias("source_c"),
        )
        .agg(pl.mean("gt_fee_in_usdt").alias("avg_fee_usdt"))
    )

    # 6. Average fees (unchanged)
    avg_fee_by_net = (
        withdrawal_fees
        .group_by(["gt_asset", "network"])
        .agg(pl.mean("gt_fee_in_usdt").alias("net_avg_fee_usdt"))
    )

    # 7. Exchange map for alternatives (keep your pattern)
    exchange_map_alt = exchange_map.select(
        "gt_source",
        pl.col("exchange").alias("alt_exchange_c"),
    )

    # 8. Base assembly (maintain your exact join sequence)
    base_result = (
        route_candidates
        .join(
            network_counts,
            left_on=["gt_asset", "gt_source_from", "gt_source_to"],
            right_on=["gt_asset", "gt_source_from", "gt_source_to"],
            how="inner",
        )
        .join(
            cheapest_alt,
            left_on=["gt_asset", "gt_source_from"],
            right_on=["gt_asset", "source_from"],
            how="left",
        )
        .join(
            exchange_map_alt,
            left_on="source_c",
            right_on="gt_source",
            how="left",
        )
        .join(
            avg_fee_by_net,
            left_on=["gt_asset", "suggested_cheaper_network"],
            right_on=["gt_asset", "network"],
            how="left",
        )
        .with_columns(
            (
                pl.col("fee_paid_usdt")
                - pl.col("net_avg_fee_usdt") * pl.col("transfer_count")
            ).alias("potential_saving_usdt")
        )
        .join(
            asset_info.filter(pl.col("can_deposit"))
            .select("gt_asset", "gt_source", "network"),
            left_on=["gt_asset", "gt_source_to", "suggested_cheaper_network"],
            right_on=["gt_asset", "gt_source", "network"],
            how="anti",
        )
        .filter(pl.col("gt_asset").str.to_lowercase() != "btc")
        .filter(
            ~(
                (pl.col("exchange_from").str.to_lowercase().str.contains("binance")
                 & pl.col("alt_exchange_c").str.to_lowercase().str.contains("binance"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("bybit")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("bybit"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("okcoinjp")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("okcoinjp"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("binance")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("tokocrypto"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("tokocrypto")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("binance"))
            )
        )
    )

    return base_result


def build_single_network_analysis(t: dict[str, pl.LazyFrame], min_savings: float = 500.0) -> pl.LazyFrame:
    """Single network analysis with minimum savings threshold."""

    base_result = build_base_pipeline(t)

    # Apply original filter for single network cases with minimum savings threshold
    fs = (
        base_result
        .filter(pl.col("network_count") == 1)
        .filter(pl.col("potential_saving_usdt") >= min_savings)
    )

    # Final result
    final = (
        fs.group_by(
            "gt_asset", "current_network", "gt_source_from", "gt_source_to",
            "exchange_from", "exchange_to", "transfer_count", "fee_paid_usdt",
            "network_count", "suggested_cheaper_network", "avg_fee_usdt",
            "potential_saving_usdt",
        )
        .agg(pl.col("alt_exchange_c").unique().alias("alt_exchanges"))
        .sort("potential_saving_usdt", descending=True, nulls_last=True)
        .limit(100)
    )

    return final