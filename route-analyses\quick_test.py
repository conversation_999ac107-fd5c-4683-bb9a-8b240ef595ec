"""
Route Analyses — optimized pipeline with smaller SQL queries
----------------------------------------------------------

* Uses targeted SQL queries instead of pulling entire tables
* Does more processing locally to reduce DB load
* Maintains the same output format and logic
"""

from __future__ import annotations

import argparse
import yaml
from datetime import datetime, timedelta, timezone
from pathlib import Path
import polars as pl


# ────────────────────────────────────────────────────────────────────────────────
# Configuration helpers
# ────────────────────────────────────────────────────────────────────────────────
def load_cfg(path: str | Path = "config.local.yml") -> dict:
    with open(path, "rt", encoding="utf-8") as fh:
        return yaml.safe_load(fh)


def pg_uri(cfg: dict) -> str:
    db = cfg["database"]
    return (
        f"postgresql://{db['user']}:{db['password']}"
        f"@{db['host']}:{db['port']}/{db['database']}"
    )


# ──────────────────────────────────────────────────────────────────────
# DB I/O helpers
# ──────────────────────────────────────────────────────────────────────
def load_targeted_data(uri: str, days: int = 7) -> dict[str, pl.LazyFrame]:
    """Load only the data we need with targeted queries."""
    
    # Calculate date filter
    cutoff_date = datetime.now(timezone.utc).replace(tzinfo=None) - timedelta(days=days)
    
    # 1. Recent transfers only (filtered at DB level)
    transfers_sql = f"""
        SELECT gt_asset, network, gt_source_from, gt_source_to, gt_fee_in_usdt
        FROM capman.transfers 
        WHERE gt_timestamp > '{cutoff_date.isoformat()}'
          AND gt_status_id BETWEEN 200 AND 299
    """
    
    # 2. Asset info - only what we need
    asset_info_sql = """
        SELECT gt_asset, network, gt_source, can_withdraw, can_deposit
        FROM capman.capman_asset_info
        WHERE can_withdraw = true OR can_deposit = true
    """
    
    # 3. Sources map - unchanged (small table)
    sources_map_sql = "SELECT gt_source, exchange FROM capman.sources_map"
    
    # 4. Withdrawal fees - only for assets that had recent transfers
    withdrawal_fees_sql = f"""
        SELECT DISTINCT wf.gt_asset, wf.network, wf.gt_source, wf.gt_fee_in_usdt
        FROM capman.withdrawal_fees wf
        WHERE EXISTS (
            SELECT 1 FROM capman.transfers t 
            WHERE t.gt_asset = wf.gt_asset 
              AND t.gt_timestamp > '{cutoff_date.isoformat()}'
              AND t.gt_status_id BETWEEN 200 AND 299
        )
    """
    
    print("[✓] Loading targeted data from Postgres...")
    
    return {
        "transfers": pl.read_database_uri(transfers_sql, uri, engine="connectorx").lazy(),
        "asset_info": pl.read_database_uri(asset_info_sql, uri, engine="connectorx").lazy(),
        "sources_map": pl.read_database_uri(sources_map_sql, uri, engine="connectorx").lazy(),
        "withdrawal_fees": pl.read_database_uri(withdrawal_fees_sql, uri, engine="connectorx").lazy(),
    }


# ──────────────────────────────────────────────────────────────────────
# Utility: prefix every column in a LazyFrame
# ──────────────────────────────────────────────────────────────────────
def rename_with_prefix(lf: pl.LazyFrame, prefix: str) -> pl.LazyFrame:
    return lf.rename({c: f"{prefix}_{c}" for c in lf.collect_schema().names()})


# ──────────────────────────────────────────────────────────────────────
# Core pipeline (one function, no globals)
# ──────────────────────────────────────────────────────────────────────
def build_optimized_pipeline(t: dict[str, pl.LazyFrame]) -> pl.LazyFrame:
    """Optimized pipeline maintaining Polars 1.31 syntax."""
    
    transfers = t["transfers"]
    asset_info = t["asset_info"]
    sources_map = t["sources_map"]
    withdrawal_fees = t["withdrawal_fees"]

    # 1. Aggregate transfers locally (unchanged syntax)
    recent_transfers = (
        transfers
        .group_by(["gt_asset", "network", "gt_source_from", "gt_source_to"])
        .agg(
            pl.len().alias("transfer_count"),
            pl.col("gt_fee_in_usdt").sum().alias("fee_paid_usdt"),
        )
    )

    # 2. Exchange mappings (keep your original approach)
    exchange_map = sources_map.select("gt_source", "exchange")
    
    sm_from = exchange_map.select(
        "gt_source",
        pl.col("exchange").alias("exchange_from"),
    )
    sm_to = exchange_map.select(
        "gt_source", 
        pl.col("exchange").alias("exchange_to"),
    )

    # 3. Route candidates (maintain your join pattern)
    route_candidates = (
        recent_transfers
        .join(sm_from, left_on="gt_source_from", right_on="gt_source")
        .join(sm_to, left_on="gt_source_to", right_on="gt_source")
        .select(
            "gt_asset",
            pl.col("network").alias("current_network"),
            "gt_source_from", "gt_source_to",
            "exchange_from", "exchange_to", 
            "transfer_count", "fee_paid_usdt",
        )
    )

    # 4. Network counts (keep your prefix approach)
    a1 = asset_info
    a2 = asset_info.rename({c: f"b_{c}" for c in asset_info.collect_schema().names()})

    network_counts = (
        a1.join(
            a2,
            left_on=["gt_asset", "network"],
            right_on=["b_gt_asset", "b_network"],
            how="inner",
        )
        .filter(
            (pl.col("gt_source") != pl.col("b_gt_source")) &
            pl.col("can_withdraw") &
            pl.col("b_can_deposit")
        )
        .group_by(
            "gt_asset",
            pl.col("gt_source").alias("gt_source_from"),
            pl.col("b_gt_source").alias("gt_source_to"),
        )
        .agg(pl.col("network").n_unique().alias("network_count"))
    )

    # 5. Cheapest alternatives (maintain your exact pattern)
    wf = withdrawal_fees.select("gt_asset", "network", "gt_source", "gt_fee_in_usdt")

    cheapest_alt = (
        a1.join(
            a2,
            left_on=["gt_asset", "network"],
            right_on=["b_gt_asset", "b_network"],
            how="inner",
        )
        .filter(pl.col("can_withdraw") & pl.col("b_can_deposit"))
        .join(
            wf,
            left_on=["gt_asset", "network", "gt_source"],
            right_on=["gt_asset", "network", "gt_source"],
            how="left",
        )
        .group_by(
            "gt_asset",
            pl.col("network").alias("suggested_cheaper_network"),
            pl.col("gt_source").alias("source_from"),
            pl.col("b_gt_source").alias("source_c"),
        )
        .agg(pl.mean("gt_fee_in_usdt").alias("avg_fee_usdt"))
    )

    # 6. Average fees (unchanged)
    avg_fee_by_net = (
        withdrawal_fees
        .group_by(["gt_asset", "network"])
        .agg(pl.mean("gt_fee_in_usdt").alias("net_avg_fee_usdt"))
    )

    # 7. Exchange map for alternatives (keep your pattern)
    exchange_map_alt = exchange_map.select(
        "gt_source",
        pl.col("exchange").alias("alt_exchange_c"),
    )

    # 8. Final assembly (maintain your exact join sequence and filters)
    fs = (
        route_candidates
        .join(
            network_counts,
            left_on=["gt_asset", "gt_source_from", "gt_source_to"],
            right_on=["gt_asset", "gt_source_from", "gt_source_to"],
            how="inner",
        )
        .join(
            cheapest_alt,
            left_on=["gt_asset", "gt_source_from"],
            right_on=["gt_asset", "source_from"],
            how="left",
        )
        .join(
            exchange_map_alt,
            left_on="source_c",
            right_on="gt_source",
            how="left",
        )
        .join(
            avg_fee_by_net,
            left_on=["gt_asset", "suggested_cheaper_network"],
            right_on=["gt_asset", "network"],
            how="left",
        )
        .with_columns(
            (
                pl.col("fee_paid_usdt")
                - pl.col("net_avg_fee_usdt") * pl.col("transfer_count")
            ).alias("potential_saving_usdt")
        )
        .filter(pl.col("network_count") == 1)
        .join(
            asset_info.filter(pl.col("can_deposit"))
            .select("gt_asset", "gt_source", "network"),
            left_on=["gt_asset", "gt_source_to", "suggested_cheaper_network"],
            right_on=["gt_asset", "gt_source", "network"],
            how="anti",
        )
        .filter(pl.col("gt_asset").str.to_lowercase() != "btc")
        .filter(
            ~(
                (pl.col("exchange_from").str.to_lowercase().str.contains("binance")
                 & pl.col("alt_exchange_c").str.to_lowercase().str.contains("binance"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("bybit")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("bybit"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("okcoinjp")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("okcoinjp"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("binance")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("tokocrypto"))
                | (pl.col("exchange_from").str.to_lowercase().str.contains("tokocrypto")
                   & pl.col("alt_exchange_c").str.to_lowercase().str.contains("binance"))
            )
        )
    )

    # 9. Final result (unchanged)
    final = (
        fs.group_by(
            "gt_asset", "current_network", "gt_source_from", "gt_source_to",
            "exchange_from", "exchange_to", "transfer_count", "fee_paid_usdt",
            "network_count", "suggested_cheaper_network", "avg_fee_usdt",
            "potential_saving_usdt",
        )
        .agg(pl.col("alt_exchange_c").unique().alias("alt_exchanges"))
        .sort("potential_saving_usdt", descending=True, nulls_last=True)
        .limit(100)
    )

    return final


def build_sql_based_pipeline(uri: str, days: int = 30) -> pl.LazyFrame:
    """Second table: SQL-based query for multi-network analysis."""

    sql_query = f"""
    WITH transfer_vol AS (
      SELECT gt_asset, network, gt_source_from, gt_source_to,
             COUNT(*) AS transfer_count,
             SUM(gt_fee_in_usdt) AS fee_paid_usdt
      FROM capman.transfers
      WHERE gt_Timestamp > NOW() - INTERVAL '{days} days'
        AND gt_status_id >= 200 AND gt_status_id < 300
      GROUP BY gt_asset, network, gt_source_from, gt_source_to
    ),
    avg_fees AS (
      SELECT gt_asset, network,
             AVG(gt_fee_in_usdt) AS avg_fee_usdt
      FROM capman.withdrawal_fees
      GROUP BY gt_asset, network
    ),
    common_networks AS MATERIALIZED (
      SELECT casf.gt_asset, smf.exchange AS exchange_from, casf.network,
             smt.exchange AS exchange_to,
             casf.gt_source AS source_from, casto.gt_source AS source_to
      FROM capman.capman_asset_info casf
      INNER JOIN capman.capman_asset_info casto
        ON casf.gt_source != casto.gt_source
       AND casf.gt_asset = casto.gt_asset
       AND casf.network = casto.network
       AND casf.can_withdraw
       AND casto.can_deposit
      INNER JOIN capman.sources_map smf ON smf.gt_source = casf.gt_source
      INNER JOIN capman.sources_map smt ON smt.gt_source = casto.gt_source
    ),
    asset_data AS (
      SELECT cn.*,
             ra IS NOT NULL AS is_active,
             wf.gt_fee_in_usdt AS fee_usdt,
             tv.transfer_count,
             tv.fee_paid_usdt,
             COUNT(cn) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to) AS network_count,
             COUNT(ra) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to) AS active_network_count,
             MIN(COALESCE(wf.gt_fee_in_usdt, avg_fee_usdt)) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to) AS cheepest_network_fee,
             FIRST_VALUE(cn.network) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to ORDER BY COALESCE(wf.gt_fee_in_usdt, avg_fee_usdt)) AS cheepest_network,
             FIRST_VALUE(ra IS NOT NULL) OVER (PARTITION BY cn.gt_asset, cn.source_from, cn.source_to ORDER BY COALESCE(wf.gt_fee_in_usdt, avg_fee_usdt)) AS cheepest_active
      FROM common_networks cn
      LEFT JOIN capman.route_activity ra
        ON ra.gt_source_from = cn.source_from
       AND ra.gt_source_to = cn.source_to
       AND ra.network = cn.network
       AND ra.gt_asset = cn.gt_asset
      LEFT JOIN capman.withdrawal_fees wf
        ON wf.gt_source = cn.source_from
       AND wf.gt_asset = cn.gt_asset
       AND wf.network = cn.network
      INNER JOIN avg_fees af
        ON af.gt_asset = cn.gt_asset
       AND af.network = cn.network
      LEFT JOIN transfer_vol tv
        ON cn.gt_asset = tv.gt_asset
       AND cn.network = tv.network
       AND cn.source_from = tv.gt_source_from
       AND cn.source_to = tv.gt_source_to
    )
    SELECT *,
           (fee_paid_usdt - cheepest_network_fee * transfer_count) AS potential_saving_usdt
    FROM asset_data
    WHERE network_count > 1
      AND active_network_count > 0
      AND network <> cheepest_network
      AND fee_paid_usdt IS NOT NULL
      AND LOWER(gt_asset) <> 'btc'
      AND NOT (
        LOWER(exchange_from) LIKE '%binance%' AND LOWER(exchange_to) LIKE '%binance%'
      )
      AND NOT (
        (LOWER(exchange_from) = 'tokocrypto' AND LOWER(exchange_to) = 'binance') OR
        (LOWER(exchange_from) = 'binance' AND LOWER(exchange_to) = 'tokocrypto')
      )
      AND LOWER(exchange_to) IN ('coinbase', 'bitopro', 'bitbank', 'btcturk_hft', 'bitso', 'indodax')
    ORDER BY potential_saving_usdt DESC
    """

    print("[✓] Loading SQL-based multi-network analysis...")
    return pl.read_database_uri(sql_query, uri, engine="connectorx").lazy()


# ──────────────────────────────────────────────────────────────────────
# CLI wrapper
# ──────────────────────────────────────────────────────────────────────
def main() -> None:
    ap = argparse.ArgumentParser()
    ap.add_argument("--config", default="config.local.yml")
    ap.add_argument("--out", default="suggested_routes.parquet")
    ap.add_argument("--out-multi", default="multi_network_inefficient.parquet",
                    help="Output file for SQL-based multi-network analysis")
    ap.add_argument("--days", type=int, default=30, help="Days to look back (default: 30)")
    args = ap.parse_args()

    cfg = load_cfg(args.config)
    uri = pg_uri(cfg)

    print(f"[✓] Using {args.days}-day lookback window")

    # Generate first table (original: single network cases)
    print("[✓] Building original pipeline (network_count == 1)...")
    tables = load_targeted_data(uri, days=args.days)
    result1 = build_optimized_pipeline(tables).collect()

    # Generate second table (SQL-based multi-network analysis)
    print("[✓] Building SQL-based multi-network analysis...")
    result2 = build_sql_based_pipeline(uri, days=args.days).collect()

    # Save first table
    out_path = Path(args.out)
    if out_path.suffix == ".parquet":
        result1.write_parquet(out_path)
    elif out_path.suffix in {".csv", ".tsv"}:
        sep = "," if out_path.suffix == ".csv" else "\t"
        result1.write_csv(out_path, separator=sep)
    elif out_path.suffix == ".json":
        result1.write_json(out_path)
    else:
        raise ValueError(f"Unsupported extension '{out_path.suffix}'")

    print(f"[✓] Wrote {len(result1)} rows to {out_path}")
    print("First table (single network) preview:")
    print(result1.head())

    # Save second table
    out_path_multi = Path(args.out_multi)
    if out_path_multi.suffix == ".parquet":
        result2.write_parquet(out_path_multi)
    elif out_path_multi.suffix in {".csv", ".tsv"}:
        sep = "," if out_path_multi.suffix == ".csv" else "\t"
        result2.write_csv(out_path_multi, separator=sep)
    elif out_path_multi.suffix == ".json":
        result2.write_json(out_path_multi)
    else:
        raise ValueError(f"Unsupported extension '{out_path_multi.suffix}'")

    print(f"[✓] Wrote {len(result2)} rows to {out_path_multi}")
    print("Second table (SQL-based multi-network analysis) preview:")
    print(result2.head())


if __name__ == "__main__":
    main()
