#!/usr/bin/env python3
"""
Simple OKX Turkey clicker that doesn't rely on the common module.
Basic functionality for opening OKX Turkey site and checking login status.
"""

import time
from typing import Optional
from selenium.webdriver import Firefox
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.firefox.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Constants
OKX_TURKEY_URL = "https://tr.okx.com"
LOGIN_URL = f"{OKX_TURKEY_URL}/en/account/login"
DEPOSIT_HISTORY_URL = f"{OKX_TURKEY_URL}/balance/deposit-history"
WITHDRAW_HISTORY_URL = f"{OKX_TURKEY_URL}/balance/withdrawal-history"


class OKXTurkeyClickerBasic:
    """Simple OKX Turkey clicker without common module dependencies."""
    
    def __init__(self, headless: bool = False):
        """Initialize the clicker with Firefox WebDriver."""
        self.driver: Firefox  # Will be initialized in setup_driver
        self.headless = headless
        self.okx_tab: Optional[str] = None
        self.setup_driver()
    
    def setup_driver(self):
        """Setup Firefox WebDriver."""
        try:
            options = Options()
            if self.headless:
                options.add_argument("--headless")
            
            # Add user agent to avoid detection
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            
            self.driver = Firefox(options=options)
            self.driver.implicitly_wait(10)
        except Exception as e:
            raise
    
    def open_okx_turkey(self):
        """Open OKX Turkey website."""
        try:
            self.driver.get(LOGIN_URL)
            self.sleep(2, "Page load")
            
            # Store the current window handle as OKX tab
            self.okx_tab = self.driver.current_window_handle
        except Exception as e:
            raise
    
    def is_logged_in(self) -> bool:
        """Check if user is logged in to OKX Turkey."""
        try:
            current_url = self.driver.current_url
            
            # Check if we're on login page
            if "login" in current_url:
                return False
            
            # Look for user profile elements that indicate login
            try:
                # Look for common logged-in indicators
                user_indicators = [
                    "//div[contains(@class, 'user')]",
                    "//span[contains(@class, 'username')]",
                    "//button[contains(@class, 'profile')]",
                    "//div[@data-testid='user-menu']"
                ]
                
                for indicator in user_indicators:
                    try:
                        element = self.driver.find_element(By.XPATH, indicator)
                        if element.is_displayed():
                            return True
                    except NoSuchElementException:
                        continue
                        
                return False
                
            except Exception as e:
                return False
                
        except Exception as e:
            return False
    
    def navigate_to_deposits(self):
        """Navigate to deposit history page."""
        try:
            self.driver.get(DEPOSIT_HISTORY_URL)
            self.sleep(3, "Deposit history page load")
        except Exception as e:
            raise
    
    def navigate_to_withdrawals(self):
        """Navigate to withdrawal history page."""
        try:
            self.driver.get(WITHDRAW_HISTORY_URL)
            self.sleep(3, "Withdrawal history page load")
        except Exception as e:
            raise
    
    def switch_to_okx_tab(self):
        """Switch to OKX tab if it exists."""
        if self.okx_tab:
            try:
                self.driver.switch_to.window(self.okx_tab)
            except Exception as e:
                # Try to find OKX tab again
                self.find_okx_tab()
        else:
            self.find_okx_tab()
    
    def find_okx_tab(self):
        """Find OKX tab among open tabs."""
        try:
            for handle in self.driver.window_handles:
                self.driver.switch_to.window(handle)
                current_url = self.driver.current_url
                
                if "tr.okx.com" in current_url:
                    self.okx_tab = handle
                    return
        except Exception as e:
            pass
    
    def sleep(self, seconds: int, reason: str = ""):
        """Sleep with logging."""
        if reason:
            print(f"Sleeping for {seconds}s: {reason}")
        else:
            print(f"Sleeping for {seconds}s")
        time.sleep(seconds)
    
    def wait_for_element(self, by, value: str, timeout: int = 10):
        """Wait for element to be present."""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            return None
    
    def check_and_handle_login(self):
        """Check login status and handle accordingly."""
        if not self.is_logged_in():
            print("User is not logged in. Please log in manually.")
            print("Navigating to login page...")
            self.driver.get(LOGIN_URL)
            self.sleep(3, "Login page load")
            
            # Wait for manual login
            print("Please log in manually. The script will wait...")
            input("Press Enter after you have logged in...")
            
            # Verify login
            if self.is_logged_in():
                print("Login successful!")
            else:
                print("Login verification failed")
                return False
        else:
            print("User is already logged in")
        
        return True
    
    def run_basic_test(self):
        """Run basic functionality test."""
        try:
            print("Starting OKX Turkey basic clicker test")
            
            # Open OKX Turkey
            self.open_okx_turkey()
            
            # Check login status
            if not self.check_and_handle_login():
                print("Login failed, stopping test")
                return False
            
            # Navigate to different pages
            self.navigate_to_deposits()
            self.sleep(2)
            
            self.navigate_to_withdrawals()
            self.sleep(2)
            
            # Go back to main page
            self.driver.get(OKX_TURKEY_URL)
            self.sleep(2)
            
            print("Basic test completed successfully!")
            return True
            
        except Exception as e:
            print(f"Basic test failed: {e}")
            return False
    
    def close(self):
        """Close the browser."""
        if self.driver:
            try:
                self.driver.quit()
                print("Browser closed successfully")
            except Exception as e:
                print(f"Error closing browser: {e}")


def main():
    """Main function to run the basic clicker."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Simple OKX Turkey clicker for testing basic functionality",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Usage Examples:
  python okx_turkey_clicker_basic.py          # Run with GUI browser
  python okx_turkey_clicker_basic.py --headless  # Run headless (no GUI)
  
Features:
  - Opens OKX Turkey website
  - Checks login status
  - Navigates to deposit/withdrawal history
  - Handles basic tab switching
  
Note: This is a basic version that does not depend on the common module.
        """
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        help="Run browser in headless mode (no GUI)"
    )
    
    args = parser.parse_args()
    
    clicker = None
    try:
        print("=== OKX Turkey Basic Clicker ===")
        print(f"Running in {'headless' if args.headless else 'GUI'} mode")
        
        # Initialize clicker
        clicker = OKXTurkeyClickerBasic(headless=args.headless)
        
        # Run basic test
        success = clicker.run_basic_test()
        
        if success:
            print("OKX Turkey basic clicker test completed successfully!")
        else:
            print("OKX Turkey basic clicker test failed!")
            
    except KeyboardInterrupt:
        print("Script interrupted by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
    finally:
        if clicker:
            clicker.close()


if __name__ == "__main__":
    main()