"""
analyses.py – replication of the Route‑Savings SQL in Polars

The main entry point is `run_route_analyses(db, days=30, limit=100)`
which returns a Polars DataFrame ready to be written to CSV.
"""

from __future__ import annotations

import polars as pl

from db import Database

# Helper regex fragments for exchange screens
_BINANCE = "binance"
_BYBIT = "bybit"
_OKCOINJP = "okcoinjp"
_TOKOCRYPTO = "tokocrypto"


def _ban_same_exchange_expr() -> pl.Expr:
    """Boolean Polars expression mirroring the NOT‑LIKE chain in SQL."""

    def _like(expr: pl.Expr, needle: str) -> pl.Expr:
        return expr.str.to_lowercase().str.contains(needle)

    ef = pl.col("exchange_from")
    et = pl.col("exchange_to")
    return (
        (_like(ef, _BINANCE) & _like(et, _BINANCE))
        | (_like(ef, _BYBIT) & _like(et, _BYBIT))
        | (_like(ef, _OKCOINJP) & _like(et, _OKCOINJP))
        | (_like(ef, _BINANCE) & _like(et, _TOKOCRYPTO))
        | (_like(ef, _TOKOCRYPTO) & _like(et, _BINANCE))
    )


def run_route_analyses(db: Database, *, days: int = 30, limit: int = 100) -> pl.DataFrame:
    """Recreates the bulky SQL using Polars lazy pipelines."""

    # ───────────────────────── RAW PULLS ──────────────────────────
    transfers_sql = f"""
        SELECT gt_asset, network,
               gt_source_from, gt_source_to,
               gt_fee_in_usdt
        FROM capman.transfers
        WHERE gt_timestamp > NOW() - INTERVAL '{days} days'
          AND gt_status_id BETWEEN 200 AND 299
    """

    asset_info_sql = """
        SELECT gt_asset, network, gt_source,
               can_withdraw, can_deposit
        FROM capman.capman_asset_info
        WHERE can_withdraw OR can_deposit
    """

    sources_map_sql = "SELECT gt_source, exchange FROM capman.sources_map"

    withdrawal_fees_sql = """
        SELECT gt_asset, network, gt_source, gt_fee_in_usdt
        FROM capman.withdrawal_fees
    """

    transfers = db.fetch_arrow(transfers_sql)
    asset_info = db.fetch_arrow(asset_info_sql)
    sources_map = db.fetch_arrow(sources_map_sql)
    withdraw_fees = db.fetch_arrow(withdrawal_fees_sql)

    # ───────────────────── CTE EQUIVALENTS ───────────────────────

    recent_transfers = (
        transfers.group_by(["gt_asset", "network", "gt_source_from", "gt_source_to"])
        .agg([
            pl.count().alias("transfer_count"),
            pl.col("gt_fee_in_usdt").sum().alias("fee_paid_usdt"),
        ])
    )

    route_candidates = (
        recent_transfers
        .join(
            sources_map.rename({"gt_source": "gt_source_from", "exchange": "exchange_from"}),
            on="gt_source_from",
        )
        .join(
            sources_map.rename({"gt_source": "gt_source_to", "exchange": "exchange_to"}),
            on="gt_source_to",
        )
        .rename({"network": "current_network"})
    )

    withdraw_side = (
        asset_info.filter(pl.col("can_withdraw"))
        .select(["gt_asset", "network", pl.col("gt_source").alias("gt_source_from")])
    )
    deposit_side = (
        asset_info.filter(pl.col("can_deposit"))
        .select(["gt_asset", "network", pl.col("gt_source").alias("gt_source_to")])
    )

    network_counts = (
        withdraw_side.join(deposit_side, on=["gt_asset", "network"])
        .filter(pl.col("gt_source_from") != pl.col("gt_source_to"))
        .group_by(["gt_asset", "gt_source_from", "gt_source_to"])
        .agg(pl.n_unique("network").alias("network_count"))
    )

    a1c = asset_info.filter(pl.col("can_withdraw")).rename({"gt_source": "source_from"})
    a2c = asset_info.filter(pl.col("can_deposit")).rename({"gt_source": "source_c"})

    # Find cheapest alternatives for each asset/network combination
    cheapest_alt = (
        withdraw_fees.join(
            asset_info.filter(pl.col("can_withdraw"))
            .select(["gt_asset", "network", "gt_source"]),
            on=["gt_asset", "network", "gt_source"],
            how="inner"
        )
        .group_by(["gt_asset", "network"])
        .agg(pl.mean("gt_fee_in_usdt").alias("avg_cheaper_fee"))
        .with_columns(pl.col("network").alias("suggested_cheaper_network"))
    )



    network_fee_avg = (
        withdraw_fees.group_by(["gt_asset", "network"])
        .agg(pl.mean("gt_fee_in_usdt").alias("avg_fee_usdt_net"))
    )

    # ───────────────────── FINAL ASSEMBLY ────────────────────────
    final = (
        route_candidates
        .join(network_counts, on=["gt_asset", "gt_source_from", "gt_source_to"])
        .join(cheapest_alt, left_on=["gt_asset", "current_network"], right_on=["gt_asset", "network"], how="left")

        .join(network_fee_avg, left_on=["gt_asset", "current_network"], right_on=["gt_asset", "network"], how="left")
        .with_columns(
            (
                pl.col("fee_paid_usdt") - pl.col("avg_cheaper_fee") * pl.col("transfer_count")
            ).alias("potential_saving_usdt")
        )
        .filter(
            (pl.col("network_count") == 1)
            & (pl.col("gt_asset").str.to_lowercase() != "btc")
            & ~_ban_same_exchange_expr()
        )
        .select([
            "gt_asset",
            "current_network",
            "gt_source_from", 
            "gt_source_to",
            "exchange_from",
            "exchange_to", 
            "transfer_count",
            "fee_paid_usdt",
            "network_count",
            "suggested_cheaper_network",
            "avg_cheaper_fee",
            "potential_saving_usdt",
        ])
        .sort("potential_saving_usdt", descending=True)
        .limit(limit)
        .collect()
    )

    # Column order must match the SQL exactly
    ordered_cols = [
        "gt_asset",
        "current_network",
        "gt_source_from",
        "gt_source_to",
        "exchange_from",
        "exchange_to",
        "transfer_count",
        "fee_paid_usdt",
        "network_count",
        "suggested_cheaper_network",
        "avg_cheaper_fee",
        "potential_saving_usdt",
    ]

    return final.select(ordered_cols)