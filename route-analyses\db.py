"""
db.py – database connection helper using psycopg3 and Polar<PERSON>

Reads connection settings from a YAML file (default: config.local.yaml)
and exposes a small API for fetching query results as <PERSON>s LazyFrames
via PostgreSQL COPY in Arrow binary format.

Usage:
    db = Database()                      # looks for config.local.yaml
    df_laz = db.fetch_arrow("SELECT 1")
"""

from pathlib import Path
from typing import Any

import yaml
import polars as pl
from psycopg_pool import ConnectionPool
from psycopg import sql


class Database:
    """Lightweight read‑only connection pool wrapper."""

    def __init__(self, cfg_path: str | Path = "config.local.yaml", min_conns: int = 1, max_conns: int = 2):
        cfg = self._load_yaml(cfg_path)
        dsn = (
            f"postgresql://{cfg['user']}:{cfg['password']}@"
            f"{cfg['host']}:{cfg.get('port', 5432)}/{cfg['database']}"
        )
        self.pool = ConnectionPool(dsn, min_size=min_conns, max_size=max_conns)

    @staticmethod
    def _load_yaml(path: str | Path) -> dict[str, Any]:
        with open(path, "r", encoding="utf-8") as fh:
            conf = yaml.safe_load(fh)
        # allow top‑level or nested under "database" key
        if "database" in conf:
            conf = conf["database"]
        required = {"user", "password", "database", "host"}
        missing = required.difference(conf)
        if missing:
            raise ValueError(f"Missing database config keys: {', '.join(sorted(missing))}")
        return conf

    def fetch_arrow(self, sql_query: str) -> pl.LazyFrame:
        """
        Execute *sql_query* and return a Polars LazyFrame.
        """
        with self.pool.connection() as conn:
            with conn.cursor() as cur:
                cur.execute(sql_query)  # type: ignore
                rows = cur.fetchall()
                columns = [desc[0] for desc in cur.description] if cur.description else []
        
        # Convert to polars DataFrame then to LazyFrame
        if rows and columns:
            df = pl.DataFrame(rows, schema=columns, orient="row")
        else:
            df = pl.DataFrame([], schema=columns if columns else [])
        
        return df.lazy()