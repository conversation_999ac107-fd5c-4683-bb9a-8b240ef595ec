#!/usr/bin/env python3
"""
Single Exchange Analysis Module for Transaction Fee Analyzer.

This module provides functionality to analyze fee data for a specific exchange
across all networks they operate on.
"""

import pandas as pd
from typing import List, <PERSON>ple


def analyze_single_exchange(transfer_fee_df: pd.DataFrame, exchange_name: str) -> pd.DataFrame:
    """
    Analyze fee data for a single exchange across all networks.

    Raises:
        ValueError: If exchange_name is not found in the data
    """
    if transfer_fee_df.empty:
        raise ValueError("Input DataFrame is empty")

    if 'exchange_from' not in transfer_fee_df.columns:
        raise ValueError("DataFrame must contain 'exchange_from' column")

    # Filter data for the specified exchange
    exchange_data = transfer_fee_df[transfer_fee_df['exchange_from'] == exchange_name].copy()

    if exchange_data.empty:
        available_exchanges = sorted(transfer_fee_df['exchange_from'].unique())
        raise ValueError(
            f"Exchange '{exchange_name}' not found in data. "
            f"Available exchanges: {available_exchanges}"
        )

    # Group by network and aggregate the metrics
    result = exchange_data.groupby('network').agg({
        'total_fee_usdt': 'sum',
        'transfer_count': 'sum'
    }).reset_index()

    # Sort by total fees descending to show highest fee networks first
    result = result.sort_values('total_fee_usdt', ascending=False)

    return result


def get_main_networks_from_exchange(transfer_fee_df: pd.DataFrame, exchange_name: str, threshold: float = 0.825) -> List[str]:
    """
    Get the main networks for a given exchange based on a threshold.

    Returns:
        List[str]: List of main network names
    """
    if transfer_fee_df.empty:
        raise ValueError("Input DataFrame is empty")

    if 'exchange_from' not in transfer_fee_df.columns or 'network' not in transfer_fee_df.columns:
        raise ValueError("DataFrame must contain 'exchange_from' and 'network' columns")

    # Filter data for the specified exchange
    exchange_data = transfer_fee_df[transfer_fee_df['exchange_from'] == exchange_name].copy()

    if exchange_data.empty:
        available_exchanges = sorted(transfer_fee_df['exchange_from'].unique())
        raise ValueError(
            f"Exchange '{exchange_name}' not found in data. "
            f"Available exchanges: {available_exchanges}"
        )

    # Group by network and aggregate the metrics
    network_analysis = exchange_data.groupby('network').agg({
        'total_fee_usdt': 'sum',
        'transfer_count': 'sum'
    }).reset_index()

    # Sort by total fees descending
    network_analysis = network_analysis.sort_values('total_fee_usdt', ascending=False)

    total_fees = network_analysis['total_fee_usdt'].sum()
    main_networks = []
    cumulative_fees = 0

    for _, row in network_analysis.iterrows():
        if cumulative_fees / total_fees >= threshold:
            break
        main_networks.append(row['network'])
        cumulative_fees += row['total_fee_usdt']

    return main_networks


def get_main_assets_for_exchange_network(transfer_fee_df: pd.DataFrame, exchange_name: str, network: str, threshold: float = 0.825) -> List[str]:
    """
    Get the main assets for a given exchange and network based on a threshold.

    Returns:
        List[str]: List of main asset names
    """
    if transfer_fee_df.empty:
        raise ValueError("Input DataFrame is empty")

    if 'exchange_from' not in transfer_fee_df.columns or 'gt_asset' not in transfer_fee_df.columns:
        raise ValueError("DataFrame must contain 'exchange_from' and 'gt_asset' columns")

    # Filter data for the specified exchange and network
    exchange_data = transfer_fee_df[(transfer_fee_df['exchange_from'] == exchange_name) & (transfer_fee_df['network'] == network)].copy()

    if exchange_data.empty:
        available_exchanges = sorted(transfer_fee_df['exchange_from'].unique())
        raise ValueError(
            f"Exchange '{exchange_name}' not found in data. "
            f"Available exchanges: {available_exchanges}"
        )

    # Group by asset and aggregate the metrics
    asset_analysis = exchange_data.groupby('gt_asset').agg({
        'total_fee_usdt': 'sum',
        'transfer_count': 'sum'
    }).reset_index()

    # Sort by total fees descending
    asset_analysis = asset_analysis.sort_values('total_fee_usdt', ascending=False)

    total_fees = asset_analysis['total_fee_usdt'].sum()
    main_assets = []
    cumulative_fees = 0

    for _, row in asset_analysis.iterrows():
        if cumulative_fees / total_fees >= threshold:
            break
        main_assets.append(row['gt_asset'])
        cumulative_fees += row['total_fee_usdt']

    return main_assets


def check_asset_dominance(transfer_fee_df: pd.DataFrame, exchange_name: str, network: str, dominance_threshold: float = 0.95) -> Tuple[bool, str, float]:
    """
    Check if a single asset has dominance in an exchange-network combination.

    Returns:
        Tuple[bool, str, float]: (has_dominance, dominant_asset, dominance_percentage)
    """
    if transfer_fee_df.empty:
        return False, "", 0.0

    # Filter data for the specified exchange and network
    exchange_data = transfer_fee_df[(transfer_fee_df['exchange_from'] == exchange_name) & (transfer_fee_df['network'] == network)].copy()

    if exchange_data.empty:
        return False, "", 0.0

    # Group by asset and aggregate the metrics
    asset_analysis = exchange_data.groupby('gt_asset').agg({
        'total_fee_usdt': 'sum'
    }).reset_index()

    # Sort by total fees descending
    asset_analysis = asset_analysis.sort_values('total_fee_usdt', ascending=False)

    total_fees = asset_analysis['total_fee_usdt'].sum()

    if len(asset_analysis) == 0 or total_fees == 0:
        return False, "", 0.0

    # Check if the top asset has dominance
    top_asset_fees = asset_analysis.iloc[0]['total_fee_usdt']
    dominance_percentage = top_asset_fees / total_fees

    if dominance_percentage >= dominance_threshold:
        return True, asset_analysis.iloc[0]['gt_asset'], dominance_percentage

    return False, "", dominance_percentage


def list_available_exchanges(transfer_fee_df: pd.DataFrame) -> List[str]:
    """
    Get a list of all available exchanges in the dataset.

    Returns:
        List[str]: Sorted list of unique exchange names
    """
    if transfer_fee_df.empty or 'exchange_from' not in transfer_fee_df.columns:
        return []

    return sorted(transfer_fee_df['exchange_from'].unique())
